package com.jykj.icm.controller;

import java.util.ArrayList;
import java.util.List;

import javax.validation.Valid;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.jykj.icm.annotation.RequireSuperAdmin;
import com.jykj.icm.common.result.Result;
import com.jykj.icm.entity.HostInfo;
import com.jykj.icm.entity.HostInfoDTO;
import com.jykj.icm.entity.HostInfoTestDTO;
import com.jykj.icm.entity.UpdateGaussPasswordRequest;
import com.jykj.icm.entity.UpdateVipRequest;
import com.jykj.icm.service.HAMonitorService;
import com.jykj.icm.service.HostConfigServer;
import com.jykj.icm.utils.AESUtils;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@RestController
@RequestMapping("/api/hostConfig")
@RequiredArgsConstructor
@Api(tags = "主机配置管理")
public class HostConfigController {
    private final HostConfigServer hostConfigServer;

    @Lazy
    @Autowired
    private HAMonitorService haMonitorService;

    @PostMapping("/update")
    @ApiOperation("保存主机配置")
    public Result<String> updateHostConfig(@RequestBody HostInfo hostInfo) {
        hostConfigServer.updateHostConfig(hostInfo);
        haMonitorService.startMonitoring();
        return Result.success("配置保存成功");
    }

    @PostMapping("/get")
    @ApiOperation("获取主机配置")
    public Result<HostInfo> getHostConfig(@RequestBody HostInfoDTO hostInfoDTO) {
        HostInfo hostInfo = hostConfigServer.getHostConfig(hostInfoDTO.getIp());
        return Result.success(hostInfo);
    }

    @PostMapping("/getAll")
    @ApiOperation("获取所有主机备机配置")
    public Result<List<HostInfo>> getAll(@RequestBody HostInfoDTO hostInfoDTO) {
        List<HostInfo> hostInfo = hostConfigServer.getAll(hostInfoDTO.getIp());
        return Result.success(hostInfo);
    }

    @PostMapping("/getAllSlave")
    @ApiOperation("获取所有备机配置")
    public Result<List<HostInfo>> getAllSlave(@RequestBody HostInfoDTO hostInfoDTO) {
        List<HostInfo> hostInfo = hostConfigServer.getAllSlave(hostInfoDTO.getIp());
        return Result.success(hostInfo);
    }

    // 配置备机的时候需要验证主机和数据库的连接
    @PostMapping("/testMasterConnection")
    @ApiOperation("测试主机连接")
    public Result<HostInfo> testMasterConnection(@Valid @RequestBody HostInfoTestDTO hostInfo) {
        HostInfo hostConfig = hostConfigServer.testConnection(hostInfo);
        return Result.success(hostConfig);
    }

    @PostMapping("/testSSHConnection")
    @ApiOperation("测试服务器连通性")
    public Result<String> testSSHConnection(@RequestBody HostInfoDTO hostInfo) {
        try {
            hostConfigServer.testSSHConnection(hostInfo.getIp(), hostInfo.getUsername(),
                AESUtils.isAESEnCode(hostInfo.getPassword()) ? AESUtils.AESDeCode(hostInfo.getPassword())
                    : hostInfo.getPassword());
            return Result.success("连接成功");
        } catch (Exception e) {
            log.error("测试{}连接失败", hostInfo.getIp(), e);
            return Result.failed(e.getMessage());
        }
    }

    @PostMapping("/testDBPassword")
    @ApiOperation("测试数据库密码是否正确")
    public Result<String> testDBPassword(@RequestBody HostInfoDTO hostInfo) {
        try {
            hostConfigServer.testDBConnection(hostInfo.getIp(), AESUtils.isAESEnCode(hostInfo.getDbPassword())
                ? AESUtils.AESDeCode(hostInfo.getDbPassword()) : hostInfo.getDbPassword());
            return Result.success("连接成功");
        } catch (Exception e) {
            log.error("测试数据库连接失败，IP: {}", hostInfo.getIp(), e);
            return Result.failed(e.getMessage());
        }
    }

    // 软还原接口
    @PostMapping("/softRestore")
    @ApiOperation("软还原")
    @RequireSuperAdmin
    public Result<String> softRestore(@RequestBody HostInfoDTO hostInfo) {
        try {
            hostConfigServer.softRestore(hostInfo);
            return Result.success("还原成功");
        } catch (Exception e) {
            log.error("还原失败，IP: {}", hostInfo.getIp(), e);
            return Result.failed(e.getMessage());
        }
    }

    @PostMapping("/updateOpenGaussPassword")
    @ApiOperation("更新数据库密码")
    @RequireSuperAdmin
    public Result<String> updateOpenGaussPassword(@RequestBody UpdateGaussPasswordRequest gaussPasswordRequest) {
        try {
            hostConfigServer.updateOpenGaussPassword(gaussPasswordRequest);
            return Result.success("更新数据库密码成功");
        } catch (Exception e) {
            log.error("更新数据库密码失败", e);
            return Result.failed(e.getMessage());
        }
    }

    @PostMapping("/updateVip")
    @ApiOperation("更新虚拟IP")
    @RequireSuperAdmin
    public Result<String> updateVip(@RequestBody UpdateVipRequest updateVipRequest) {
        try {
            hostConfigServer.updateVip(updateVipRequest);
            return Result.success("更新虚拟IP成功");
        } catch (Exception e) {
            log.error("更新虚拟IP失败", e);
            return Result.failed(e.getMessage());
        }
    }

    @PostMapping("/updateSso")
    @ApiOperation("更新SSO服务配置")
    @RequireSuperAdmin
    public Result<String> updateSso(@RequestBody HostInfoDTO hostInfoDTO) {
        try {
            // 获取当前主机配置
            HostInfo hostInfo = hostConfigServer.getHostConfig(hostInfoDTO.getIp());
            if (hostInfo == null) {
                return Result.failed("未找到主机配置信息");
            }

            // 调用SSO更新方法
            hostConfigServer.updateSso(hostInfo, new ArrayList<>());
            return Result.success("SSO服务配置更新成功");
        } catch (Exception e) {
            log.error("更新SSO服务配置失败", e);
            return Result.failed(e.getMessage());
        }
    }

    @PostMapping("/updateGateway")
    @ApiOperation("更新网关服务配置")
    @RequireSuperAdmin
    public Result<String> updateGateway(@RequestBody HostInfoDTO hostInfoDTO) {
        try {
            // 获取当前主机配置
            HostInfo hostInfo = hostConfigServer.getHostConfig(hostInfoDTO.getIp());
            if (hostInfo == null) {
                return Result.failed("未找到主机配置信息");
            }

            // 更新网关配置
            hostConfigServer.updateGateway(hostInfo, new ArrayList<>());
            return Result.success("网关服务配置更新成功");
        } catch (Exception e) {
            log.error("更新网关服务配置失败", e);
            return Result.failed(e.getMessage());
        }
    }
}
