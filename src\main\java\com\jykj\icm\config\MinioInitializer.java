package com.jykj.icm.config;

import io.minio.BucketExistsArgs;
import io.minio.MakeBucketArgs;
import io.minio.MinioClient;
import io.minio.SetBucketPolicyArgs;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.CommandLineRunner;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.annotation.Order;

/**
 * MinIO Bucket初始化器
 * <p>
 * 应用启动时检查并创建所需的MinIO Bucket.
 * 此初始化器仅用于确保Bucket存在，不干扰x-file-storage库的MinioClient自动配置。
 *
 * <AUTHOR> Team (Refactored)
 */
@Configuration
public class MinioInitializer {

    private static final Logger logger = LoggerFactory.getLogger(MinioInitializer.class);

    @Value("${dromara.x-file-storage.minio[0].end-point}")
    private String endpoint;

    @Value("${dromara.x-file-storage.minio[0].access-key}")
    private String accessKey;

    @Value("${dromara.x-file-storage.minio[0].secret-key}")
    private String secretKey;

    @Value("${dromara.x-file-storage.minio[0].bucket-name}")
    private String bucketName;

    /**
     * 创建一个临时的、不被Spring管理的MinIO客户端，仅用于初始化。
     */
    public MinioClient createTemporaryMinioClient() {
        return createTemporaryMinioClient(endpoint, accessKey, secretKey);
    }

    /**
     * 创建一个临时的MinIO客户端，支持自定义连接参数
     *
     * @param endpoint MinIO端点
     * @param accessKey 访问密钥
     * @param secretKey 秘密密钥
     * @return MinIO客户端
     */
    public static MinioClient createTemporaryMinioClient(String endpoint, String accessKey, String secretKey) {
        try {
            return MinioClient.builder()
                    .endpoint(endpoint)
                    .credentials(accessKey, secretKey)
                    .build();
        } catch (Exception e) {
            throw new RuntimeException("创建临时MinIO客户端失败: " + e.getMessage(), e);
        }
    }

    /**
     * 应用启动时执行，用于初始化Bucket。
     */
    @Bean
    @Order(1)
    public CommandLineRunner initializeBucket() {
        return args -> {
            // 使用临时客户端进行初始化操作
            MinioClient minioClient = createTemporaryMinioClient();
            try {
                logger.info("开始检查MinIO bucket: {}", bucketName);

                // 检查bucket是否存在
                boolean bucketExists = minioClient.bucketExists(
                        BucketExistsArgs.builder()
                                .bucket(bucketName)
                                .build()
                );

                if (!bucketExists) {
                    // 创建bucket
                    minioClient.makeBucket(
                            MakeBucketArgs.builder()
                                    .bucket(bucketName)
                                    .build()
                    );
                    logger.info("✅ 成功创建MinIO bucket: {}", bucketName);
                } else {
                    logger.info("✅ MinIO bucket已存在: {}", bucketName);
                }

                // 设置bucket为public读取权限
                setBucketPublicReadPolicy(minioClient, bucketName);

                logger.info("MinIO Bucket初始化检查完成");

            } catch (Exception e) {
                logger.error("❌ 初始化MinIO bucket失败: {}", bucketName, e);
                // 不抛出异常，避免影响应用启动，但记录错误
                logger.warn("MinIO初始化失败，文件上传功能可能不可用");
            }
        };
    }

    /**
     * 设置bucket为公共读取权限
     *
     * @param minioClient MinIO客户端
     * @param bucketName  bucket名称
     */
    private void setBucketPublicReadPolicy(MinioClient minioClient, String bucketName) {
        try {
            // 构建公共读取策略JSON
            String publicReadPolicy = "{\n" +
                    "  \"Version\": \"2012-10-17\",\n" +
                    "  \"Statement\": [\n" +
                    "    {\n" +
                    "      \"Effect\": \"Allow\",\n" +
                    "      \"Principal\": {\n" +
                    "        \"AWS\": \"*\"\n" +
                    "      },\n" +
                    "      \"Action\": [\n" +
                    "        \"s3:GetObject\"\n" +
                    "      ],\n" +
                    "      \"Resource\": [\n" +
                    "        \"arn:aws:s3:::" + bucketName + "/*\"\n" +
                    "      ]\n" +
                    "    }\n" +
                    "  ]\n" +
                    "}";

            // 设置bucket策略
            minioClient.setBucketPolicy(
                    SetBucketPolicyArgs.builder()
                            .bucket(bucketName)
                            .config(publicReadPolicy)
                            .build()
            );

            logger.info("✅ 成功设置bucket公共读取权限: {}", bucketName);

        } catch (Exception e) {
            logger.error("❌ 设置bucket公共读取权限失败: {}", bucketName, e);
            logger.warn("文件可能无法通过URL直接访问，请手动设置bucket权限");
        }
    }

    /**
     * 使用配置文件中的认证信息初始化指定endpoint的bucket
     *
     * @param customEndpoint 自定义MinIO端点
     */
    public void initializeBucketWithCustomEndpoint(String customEndpoint) {
        try {
            logger.info("开始初始化MinIO bucket: {} (自定义endpoint: {})", bucketName, customEndpoint);

            // 等待MinIO服务启动
            Thread.sleep(5000);

            // 使用配置文件中的认证信息创建临时客户端
            MinioClient minioClient = MinioClient.builder()
                    .endpoint(customEndpoint)
                    .credentials(accessKey, secretKey)
                    .build();

            // 检查bucket是否存在
            boolean bucketExists = minioClient.bucketExists(
                    BucketExistsArgs.builder()
                            .bucket(bucketName)
                            .build()
            );

            if (!bucketExists) {
                // 创建bucket
                minioClient.makeBucket(
                        MakeBucketArgs.builder()
                                .bucket(bucketName)
                                .build()
                );
                logger.info("✅ 成功创建MinIO bucket: {}", bucketName);
            } else {
                logger.info("✅ MinIO bucket已存在: {}", bucketName);
            }

            // 设置bucket为公共读取权限
            setBucketPublicReadPolicy(minioClient, bucketName);

            logger.info("MinIO bucket初始化完成: {}", bucketName);

        } catch (Exception e) {
            logger.error("❌ 初始化MinIO bucket失败: {} (endpoint: {})", bucketName, customEndpoint, e);
            logger.warn("MinIO bucket初始化失败，文件上传功能可能不可用");
        }
    }

    /**
     * 初始化指定MinIO实例的bucket
     *
     * @param endpoint MinIO端点
     * @param accessKey 访问密钥
     * @param secretKey 秘密密钥
     * @param bucketName bucket名称
     */
    public static void initializeBucket(String endpoint, String accessKey, String secretKey, String bucketName) {
        Logger logger = LoggerFactory.getLogger(MinioInitializer.class);

        try {
            logger.info("开始初始化MinIO bucket: {} (endpoint: {})", bucketName, endpoint);

            // 等待MinIO服务启动
            Thread.sleep(5000);

            // 创建临时MinIO客户端
            MinioClient minioClient = createTemporaryMinioClient(endpoint, accessKey, secretKey);

            // 检查bucket是否存在
            boolean bucketExists = minioClient.bucketExists(
                    BucketExistsArgs.builder()
                            .bucket(bucketName)
                            .build()
            );

            if (!bucketExists) {
                // 创建bucket
                minioClient.makeBucket(
                        MakeBucketArgs.builder()
                                .bucket(bucketName)
                                .build()
                );
                logger.info("✅ 成功创建MinIO bucket: {}", bucketName);
            } else {
                logger.info("✅ MinIO bucket已存在: {}", bucketName);
            }

            // 设置bucket为公共读取权限
            setBucketPublicReadPolicyStatic(minioClient, bucketName);

            logger.info("MinIO bucket初始化完成: {}", bucketName);

        } catch (Exception e) {
            logger.error("❌ 初始化MinIO bucket失败: {} (endpoint: {})", bucketName, endpoint, e);
            logger.warn("MinIO bucket初始化失败，文件上传功能可能不可用");
        }
    }

    /**
     * 静态方法：设置bucket为公共读取权限
     *
     * @param minioClient MinIO客户端
     * @param bucketName bucket名称
     */
    private static void setBucketPublicReadPolicyStatic(MinioClient minioClient, String bucketName) {
        Logger logger = LoggerFactory.getLogger(MinioInitializer.class);

        try {
            // 构建公共读取策略JSON
            String publicReadPolicy = "{\n" +
                    "  \"Version\": \"2012-10-17\",\n" +
                    "  \"Statement\": [\n" +
                    "    {\n" +
                    "      \"Effect\": \"Allow\",\n" +
                    "      \"Principal\": {\n" +
                    "        \"AWS\": \"*\"\n" +
                    "      },\n" +
                    "      \"Action\": [\n" +
                    "        \"s3:GetObject\"\n" +
                    "      ],\n" +
                    "      \"Resource\": [\n" +
                    "        \"arn:aws:s3:::" + bucketName + "/*\"\n" +
                    "      ]\n" +
                    "    }\n" +
                    "  ]\n" +
                    "}";

            // 设置bucket策略
            minioClient.setBucketPolicy(
                    SetBucketPolicyArgs.builder()
                            .bucket(bucketName)
                            .config(publicReadPolicy)
                            .build()
            );

            logger.info("✅ 成功设置bucket公共读取权限: {}", bucketName);

        } catch (Exception e) {
            logger.error("❌ 设置bucket公共读取权限失败: {}", bucketName, e);
            logger.warn("文件可能无法通过URL直接访问，请手动设置bucket权限");
        }
    }
}