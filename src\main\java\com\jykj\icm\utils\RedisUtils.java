package com.jykj.icm.utils;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.springframework.data.redis.connection.MessageListener;
import org.springframework.data.redis.connection.RedisStandaloneConfiguration;
import org.springframework.data.redis.connection.lettuce.LettuceConnectionFactory;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.listener.ChannelTopic;
import org.springframework.data.redis.listener.RedisMessageListenerContainer;
import org.springframework.data.redis.serializer.GenericJackson2JsonRedisSerializer;
import org.springframework.data.redis.serializer.StringRedisSerializer;
import org.springframework.stereotype.Component;

import com.google.common.collect.Lists;
import com.jykj.icm.common.Constants;

import lombok.extern.slf4j.Slf4j;

@Slf4j
@Component
public class RedisUtils {
    private static final int MAX_RETRY_ATTEMPTS = 30;
    private static final long RETRY_DELAY_MS = 1000;

    /**
     * 创建 RedisTemplate
     *
     * @param host Redis 主机地址
     * @param port Redis 端口
     * @param password Redis 密码
     * @return RedisTemplate 实例
     */
    private RedisTemplate<String, Object> createRedisTemplate(String host, int port, String password) {
        RedisStandaloneConfiguration config = new RedisStandaloneConfiguration();
        config.setHostName(host);
        config.setPort(port);
        if (password != null && !password.isEmpty()) {
            config.setPassword(password);
        }

        LettuceConnectionFactory connectionFactory = new LettuceConnectionFactory(config);
        connectionFactory.afterPropertiesSet();

        RedisTemplate<String, Object> template = new RedisTemplate<>();
        template.setConnectionFactory(connectionFactory);
        template.setKeySerializer(new StringRedisSerializer());
        template.setValueSerializer(new GenericJackson2JsonRedisSerializer());
        template.setHashKeySerializer(new StringRedisSerializer());
        template.setHashValueSerializer(new GenericJackson2JsonRedisSerializer());
        template.afterPropertiesSet();

        return template;
    }

    /**
     * 尝试连接 Redis，如果连接失败则重试
     *
     * @param host Redis 主机地址
     * @param port Redis 端口
     * @param password Redis 密码
     * @return RedisTemplate 实例，如果连接失败返回 null
     */
    public RedisTemplate<String, Object> waitForRedisConnection(String host, int port, String password) {
        RedisTemplate<String, Object> template = null;

        for (int attempt = 1; attempt <= MAX_RETRY_ATTEMPTS; attempt++) {
            try {
                template = createRedisTemplate(host, port, password);
                template.getConnectionFactory().getConnection().ping();
                log.info("Successfully connected to Redis at {}:{}", host, port);
                return template;
            } catch (Exception e) {
                log.warn("Attempt {} of {} to connect to Redis at {}:{} failed: {}", attempt, MAX_RETRY_ATTEMPTS, host,
                    port, e.getMessage());
                if (template != null) {
                    try {
                        LettuceConnectionFactory factory = (LettuceConnectionFactory)template.getConnectionFactory();
                        factory.destroy();
                    } catch (Exception ex) {
                        log.warn("Error destroying connection factory", ex);
                    }
                }

                if (attempt < MAX_RETRY_ATTEMPTS) {
                    try {
                        Thread.sleep(RETRY_DELAY_MS);
                    } catch (InterruptedException ie) {
                        Thread.currentThread().interrupt();
                        return null;
                    }
                }
            }
        }
        log.error("Failed to connect to Redis at {}:{} after {} attempts", host, port, MAX_RETRY_ATTEMPTS);
        return null;
    }

    /**
     * 将对象存储到 Redis
     *
     * @param template RedisTemplate 实例
     * @param key 键
     * @param value 值
     */
    public void set(RedisTemplate<String, Object> template, String key, Object value) {
        try {
            template.opsForValue().set(key, value);
            log.info("Successfully stored value for key: {}", key);
        } catch (Exception e) {
            log.error("Failed to store value for key {}: {}", key, e.getMessage());
            throw e;
        }
    }

    /**
     * 获取 Redis 中的对象
     *
     * @param template RedisTemplate 实例
     * @param key 键
     * @return 值
     */
    public Object get(RedisTemplate<String, Object> template, String key) {
        try {
            return template.opsForValue().get(key);
        } catch (Exception e) {
            log.error("Failed to get value for key {}: {}", key, e.getMessage());
            throw e;
        }
    }

    /**
     * 获取所有以指定前缀的键
     *
     * @param template RedisTemplate 实例
     * @param prefix 前缀
     * @return 键列表
     */
    public Iterable<String> keys(RedisTemplate<String, Object> template, String prefix) {
        try {
            return template.keys(prefix + "*");
        } catch (Exception e) {
            log.error("Failed to get keys with prefix {}: {}", prefix, e.getMessage());
            throw e;
        }
    }

    /**
     * 删除 Redis 中的对象
     *
     * @param template RedisTemplate 实例
     * @param key 键
     */
    public void delete(RedisTemplate<String, Object> template, String key) {
        try {
            template.delete(key);
            log.info("Successfully deleted key: {}", key);
        } catch (Exception e) {
            log.error("Failed to delete key {}: {}", key, e.getMessage());
            throw e;
        }
    }

    public void redisSet(String ip, String key, String value) {
        try {
            RedisTemplate<String, Object> template =
                waitForRedisConnection(ip, Integer.parseInt(Constants.REDIS_PORT), Constants.DEFAULT_PASSWORD);

            if (template != null) {
                // 采用HostInfo::getLocalIp()作为key，将HostInfo对象存储到Redis中
                set(template, key, value);
                log.info("Successfully stored host config in Redis for IP: {}", ip);

                // 关闭连接
                try {
                    LettuceConnectionFactory factory = (LettuceConnectionFactory)template.getConnectionFactory();
                    factory.destroy();
                } catch (Exception ex) {
                    log.warn("Error destroying connection factory", ex);
                }
            } else {
                log.error("Failed to connect to Redis after max retries for IP: {}", ip);
            }
        } catch (Exception e) {
            log.error("Error storing host config in Redis for IP {}: {}", ip, e.getMessage());
        }
    }

    public List redisGetKey(String ip, String key) {
        try {
            RedisTemplate<String, Object> template =
                waitForRedisConnection(ip, Integer.parseInt(Constants.REDIS_PORT), Constants.DEFAULT_PASSWORD);

            if (template != null) {
                Iterable<String> keys = keys(template, key);
                log.info("Successfully stored host config in Redis for IP: {}", ip);
                List<String> keyList = Lists.newArrayList(keys);
                // 关闭连接
                try {
                    LettuceConnectionFactory factory = (LettuceConnectionFactory)template.getConnectionFactory();
                    factory.destroy();
                } catch (Exception ex) {
                    log.warn("Error destroying connection factory", ex);
                }
                return keyList;
            } else {
                log.error("Failed to connect to Redis after max retries for IP: {}", ip);
            }
        } catch (Exception e) {
            log.error("Error storing host config in Redis for IP {}: {}", ip, e.getMessage());
        }
        return null;
    }

    public List redisGetKey(RedisTemplate<String, Object> template, String key) {
        try {
            if (template != null) {
                Iterable<String> keys = keys(template, key);
                log.info("Successfully stored host config in Redis for key: {}", key);
                List<String> keyList = Lists.newArrayList(keys);
                log.info("获取到的key列表为：{}", keyList);
                // 关闭连接
                try {
                    LettuceConnectionFactory factory = (LettuceConnectionFactory)template.getConnectionFactory();
                    factory.destroy();
                } catch (Exception ex) {
                    log.warn("Error destroying connection factory", ex);
                }
                return keyList;
            } else {
                log.error("Failed to connect to Redis after max retries for key: {}", key);
            }
        } catch (Exception e) {
            log.error("Error storing host config in Redis for key {}: {}", key, e.getMessage());
        }
        return null;
    }

    public Object redisGet(String ip, String key) {
        try {
            RedisTemplate<String, Object> template =
                waitForRedisConnection(ip, Integer.parseInt(Constants.REDIS_PORT), Constants.DEFAULT_PASSWORD);

            if (template != null) {
                // 采用HostInfo::getLocalIp()作为key，将HostInfo对象存储到Redis中
                Object value = get(template, key);
                log.info("Successfully stored host config in Redis for IP: {}", ip);
                // 关闭连接
                try {
                    LettuceConnectionFactory factory = (LettuceConnectionFactory)template.getConnectionFactory();
                    factory.destroy();
                } catch (Exception ex) {
                    log.warn("Error destroying connection factory", ex);
                }
                return value;
            } else {
                log.error("Failed to connect to Redis after max retries for IP: {}", ip);
            }
        } catch (Exception e) {
            log.error("Error storing host config in Redis for IP {}: {}", ip, e.getMessage());
        }
        return null;
    }

    /**
     * 发布消息到指定频道
     *
     * @param ip Redis 主机地址
     * @param channel 频道名称
     * @param message 消息内容
     */
    public void publish(String ip, String channel, String message) {
        try {
            RedisTemplate<String, Object> template =
                waitForRedisConnection(ip, Integer.parseInt(Constants.REDIS_PORT), Constants.DEFAULT_PASSWORD);

            if (template != null) {
                template.convertAndSend(channel, message);
                log.info("Successfully published message to channel {} on Redis {}", channel, ip);

                // 关闭连接
                try {
                    LettuceConnectionFactory factory = (LettuceConnectionFactory)template.getConnectionFactory();
                    factory.destroy();
                } catch (Exception ex) {
                    log.warn("Error destroying connection factory", ex);
                }
            } else {
                log.error("Failed to connect to Redis after max retries for IP: {}", ip);
            }
        } catch (Exception e) {
            log.error("Error publishing message to Redis for IP {}: {}", ip, e.getMessage());
        }
    }

    /**
     * 订阅指定频道的消息
     *
     * @param ip Redis 主机地址
     * @param channel 频道名称
     * @param listener 消息监听器
     */
    public void subscribe(String ip, String channel, MessageListener listener) {
        try {
            RedisTemplate<String, Object> template =
                waitForRedisConnection(ip, Integer.parseInt(Constants.REDIS_PORT), Constants.DEFAULT_PASSWORD);

            if (template != null) {
                RedisMessageListenerContainer container = new RedisMessageListenerContainer();
                container.setConnectionFactory(template.getConnectionFactory());
                container.addMessageListener(listener, new ChannelTopic(channel));
                container.afterPropertiesSet();
                container.start();
                log.info("Successfully subscribed to channel {} on Redis {}", channel, ip);
            } else {
                log.error("Failed to connect to Redis after max retries for IP: {}", ip);
            }
        } catch (Exception e) {
            log.error("Error subscribing to Redis for IP {}: {}", ip, e.getMessage());
        }
    }

    /**
     * 将对象存储到 Redis Hash 中
     *
     * @param key Hash 的键
     * @param object 要存储的对象
     */
    public <T> void setHash(String key, T object) {
        try {
            RedisTemplate<String, Object> template = waitForRedisConnection(Constants.REDIS_HOST,
                Integer.parseInt(Constants.REDIS_PORT), Constants.DEFAULT_PASSWORD);

            if (template != null) {
                template.opsForHash().putAll(key, objectToMap(object));
                log.info("Successfully stored hash for key: {}", key);

                // 关闭连接
                try {
                    LettuceConnectionFactory factory = (LettuceConnectionFactory)template.getConnectionFactory();
                    factory.destroy();
                } catch (Exception ex) {
                    log.warn("Error destroying connection factory", ex);
                }
            } else {
                log.error("Failed to connect to Redis after max retries");
            }
        } catch (Exception e) {
            log.error("Failed to store hash for key {}: {}", key, e.getMessage());
        }
    }

    /**
     * 将对象存储到 Redis Hash 的字段中
     *
     * @param key Hash 的键
     * @param hashKey Hash 的字段
     * @param object 要存储的对象
     */
    public <T> void setHashKey(String key, String hashKey, T object) {
        try {
            RedisTemplate<String, Object> template = waitForRedisConnection(Constants.REDIS_HOST,
                Integer.parseInt(Constants.REDIS_PORT), Constants.DEFAULT_PASSWORD);

            if (template != null) {
                template.opsForHash().put(key, hashKey, object);
                log.info("Successfully stored hash field {} for key: {}", hashKey, key);

                // 关闭连接
                try {
                    LettuceConnectionFactory factory = (LettuceConnectionFactory)template.getConnectionFactory();
                    factory.destroy();
                } catch (Exception ex) {
                    log.warn("Error destroying connection factory", ex);
                }
            } else {
                log.error("Failed to connect to Redis after max retries");
            }
        } catch (Exception e) {
            log.error("Failed to store hash field {} for key {}: {}", hashKey, key, e.getMessage());
        }
    }

    /**
     * 获取 Redis Hash 中的对象
     *
     * @param key Hash 的键
     * @param clazz 对象类型
     * @return 对象
     */
    public <T> T getHash(String key, Class<T> clazz) {
        try {
            RedisTemplate<String, Object> template = waitForRedisConnection(Constants.REDIS_HOST,
                Integer.parseInt(Constants.REDIS_PORT), Constants.DEFAULT_PASSWORD);

            if (template != null) {
                Map<Object, Object> entries = template.opsForHash().entries(key);

                // 关闭连接
                try {
                    LettuceConnectionFactory factory = (LettuceConnectionFactory)template.getConnectionFactory();
                    factory.destroy();
                } catch (Exception ex) {
                    log.warn("Error destroying connection factory", ex);
                }

                if (entries != null && !entries.isEmpty()) {
                    // 使用 Jackson 或其他方式将 Map 转换为对象
                    return mapToObject(entries, clazz);
                }
            } else {
                log.error("Failed to connect to Redis after max retries");
            }
        } catch (Exception e) {
            log.error("Failed to get hash for key {}: {}", key, e.getMessage());
        }

        return null;
    }

    /**
     * 获取 Redis Hash 中字段的值
     *
     * @param key Hash 的键
     * @param hashKey Hash 的字段
     * @param clazz 值的类型
     * @return 值
     */
    public <T> T getHashKey(String key, String hashKey, Class<T> clazz) {
        try {
            RedisTemplate<String, Object> template = waitForRedisConnection(Constants.REDIS_HOST,
                Integer.parseInt(Constants.REDIS_PORT), Constants.DEFAULT_PASSWORD);

            if (template != null) {
                Object value = template.opsForHash().get(key, hashKey);

                // 关闭连接
                try {
                    LettuceConnectionFactory factory = (LettuceConnectionFactory)template.getConnectionFactory();
                    factory.destroy();
                } catch (Exception ex) {
                    log.warn("Error destroying connection factory", ex);
                }

                if (value != null) {
                    // 使用 Jackson 或其他方式将值转换为对象
                    return convertValue(value, clazz);
                }
            } else {
                log.error("Failed to connect to Redis after max retries");
            }
        } catch (Exception e) {
            log.error("Failed to get hash field {} for key {}: {}", hashKey, key, e.getMessage());
        }

        return null;
    }

    /**
     * 获取 Redis Hash 中的所有字段
     *
     * @param key Hash 的键
     * @param clazz 值的类型
     * @return 字段和值的映射
     */
    public <T> Map<String, T> getHashAll(String key, Class<T> clazz) {
        try {
            RedisTemplate<String, Object> template = waitForRedisConnection(Constants.REDIS_HOST,
                Integer.parseInt(Constants.REDIS_PORT), Constants.DEFAULT_PASSWORD);

            if (template != null) {
                Map<Object, Object> entries = template.opsForHash().entries(key);
                Map<String, T> result = new HashMap<>();

                // 关闭连接
                try {
                    LettuceConnectionFactory factory = (LettuceConnectionFactory)template.getConnectionFactory();
                    factory.destroy();
                } catch (Exception ex) {
                    log.warn("Error destroying connection factory", ex);
                }

                if (entries != null && !entries.isEmpty()) {
                    for (Map.Entry<Object, Object> entry : entries.entrySet()) {
                        String fieldKey = entry.getKey().toString();
                        T value = convertValue(entry.getValue(), clazz);
                        result.put(fieldKey, value);
                    }
                }

                return result;
            } else {
                log.error("Failed to connect to Redis after max retries");
            }
        } catch (Exception e) {
            log.error("Failed to get all hash fields for key {}: {}", key, e.getMessage());
        }

        return new HashMap<>();
    }

    /**
     * 将对象转换为 Map
     *
     * @param object 要转换的对象
     * @return Map
     */
    private <T> Map<String, Object> objectToMap(T object) {
        try {
            // 这里可以使用 Jackson 或其他方式将对象转换为 Map
            // 简化实现，实际项目中应该使用 ObjectMapper
            return new HashMap<>();
        } catch (Exception e) {
            log.error("Failed to convert object to map: {}", e.getMessage());
            return new HashMap<>();
        }
    }

    /**
     * 将 Map 转换为对象
     *
     * @param map 要转换的 Map
     * @param clazz 对象类型
     * @return 对象
     */
    private <T> T mapToObject(Map<Object, Object> map, Class<T> clazz) {
        try {
            // 这里可以使用 Jackson 或其他方式将 Map 转换为对象
            // 简化实现，实际项目中应该使用 ObjectMapper
            return clazz.newInstance();
        } catch (Exception e) {
            log.error("Failed to convert map to object: {}", e.getMessage());
            return null;
        }
    }

    /**
     * 转换值为指定类型
     *
     * @param value 要转换的值
     * @param clazz 目标类型
     * @return 转换后的值
     */
    @SuppressWarnings("unchecked")
    private <T> T convertValue(Object value, Class<T> clazz) {
        try {
            // 这里可以使用 Jackson 或其他方式将值转换为指定类型
            // 简化实现，实际项目中应该使用 ObjectMapper
            if (value.getClass().isAssignableFrom(clazz)) {
                return (T)value;
            }
            return clazz.newInstance();
        } catch (Exception e) {
            log.error("Failed to convert value: {}", e.getMessage());
            return null;
        }
    }
}